#!/usr/bin/env python3
"""
语音ROM分区上传脚本
在编译后自动将语音ROM文件上传到voice_rom分区
"""

import os
import sys
import subprocess
from pathlib import Path

# PlatformIO环境配置
Import("env")

# 添加自定义上传目标
def upload_voice_rom_target(*args, **kwargs):
    """自定义上传目标 - 上传语音ROM到分区"""
    upload_voice_rom(None, None, env)

env.AddCustomTarget(
    name="upload_voice_rom",
    dependencies=None,
    actions=[upload_voice_rom_target],
    title="Upload Voice ROM",
    description="Upload voice ROM file to voice_rom partition"
)

def upload_voice_rom(source, target, env):
    """PlatformIO后处理钩子函数 - 上传语音ROM到分区"""
    print("Uploading voice ROM to partition...")
    
    # 获取项目根目录
    project_dir = env.get("PROJECT_DIR")
    voice_rom_file = os.path.join(project_dir, "src", "voice_rom.bin")
    
    # 检查ROM文件是否存在
    if not os.path.exists(voice_rom_file):
        print(f"Warning: Voice ROM file not found: {voice_rom_file}")
        return
    
    # 获取文件大小
    file_size = os.path.getsize(voice_rom_file)
    print(f"Voice ROM file size: {file_size} bytes")
    
    # 检查文件大小是否超过分区大小 (320KB = 327680 bytes)
    max_size = 0x50000  # 320KB
    if file_size > max_size:
        print(f"Error: Voice ROM file too large: {file_size} > {max_size}")
        return
    
    # 获取上传端口
    upload_port = env.get("UPLOAD_PORT", "")
    if not upload_port:
        # 尝试自动检测端口
        try:
            result = subprocess.run(["pio", "device", "list"], 
                                  capture_output=True, text=True, check=True)
            lines = result.stdout.split('\n')
            for line in lines:
                if 'USB' in line and 'COM' in line:
                    # 提取COM端口
                    parts = line.split()
                    for part in parts:
                        if part.startswith('COM'):
                            upload_port = part
                            break
                    if upload_port:
                        break
        except:
            pass
    
    if not upload_port:
        print("Warning: No upload port specified, skipping voice ROM upload")
        print("To upload manually, use:")
        print(f"esptool.py --port <PORT> --baud 921600 write_flash 0x5D0000 {voice_rom_file}")
        return
    
    # 构建esptool命令
    esptool_cmd = [
        "esptool.py",
        "--port", upload_port,
        "--baud", "921600",
        "write_flash",
        "0x5D0000",  # voice_rom分区的起始地址
        voice_rom_file
    ]
    
    try:
        print(f"Uploading voice ROM to {upload_port}...")
        print(f"Command: {' '.join(esptool_cmd)}")
        
        result = subprocess.run(esptool_cmd, check=True, capture_output=True, text=True)
        print("Voice ROM uploaded successfully!")
        print(result.stdout)
        
    except subprocess.CalledProcessError as e:
        print(f"Failed to upload voice ROM: {e}")
        print(f"Error output: {e.stderr}")
        print("\nTo upload manually, use:")
        print(f"esptool.py --port {upload_port} --baud 921600 write_flash 0x5D0000 {voice_rom_file}")
    except FileNotFoundError:
        print("Error: esptool.py not found in PATH")
        print("Please install esptool: pip install esptool")
        print("\nTo upload manually, use:")
        print(f"esptool.py --port {upload_port} --baud 921600 write_flash 0x5D0000 {voice_rom_file}")

def main():
    """独立运行时的主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Upload voice ROM to ESP32 partition')
    parser.add_argument('--project-dir', required=True, help='Project directory')
    parser.add_argument('--port', help='Upload port')
    
    args = parser.parse_args()
    
    # 模拟环境变量
    class MockEnv:
        def get(self, key):
            if key == "PROJECT_DIR":
                return args.project_dir
            elif key == "UPLOAD_PORT":
                return args.port or ""
            return None
    
    upload_voice_rom(None, None, MockEnv())

if __name__ == '__main__':
    main()
