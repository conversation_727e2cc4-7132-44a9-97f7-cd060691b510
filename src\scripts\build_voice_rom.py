#!/usr/bin/env python3
"""
PlatformIO构建脚本集成
在编译时自动生成语音ROM文件
"""

import os
import sys
import subprocess
import requests
import time
import struct
import zlib
from pathlib import Path

# 添加当前脚本目录到Python路径
# 在PlatformIO构建环境中，__file__可能未定义，需要特殊处理
try:
    script_dir = Path(__file__).parent
except NameError:
    # 如果__file__未定义，使用当前工作目录的scripts子目录
    script_dir = Path(os.getcwd()) / "src" / "scripts"
sys.path.insert(0, str(script_dir))

from embed_voice_files import VoiceROMBuilder, VOICE_COMPRESSION_ADPCM

# 语音文件下载配置
VOICE_BASE_URL = "https://d.gto.so/gateway/voice/"
VOICE_FILES = [
    "open_app_to_config",
    "network_success",
    "select_user",
    "blood_pressure_data",
    "temperature_data",
    "weight_data",
    "blood_glucose_data",
    "blood_oxygen_data",
    "tap_smart_config",
    "new_message"
]

def download_voice_file(filename, voice_files_dir, max_retries=3):
    """从网络下载语音文件"""
    url = f"{VOICE_BASE_URL}{filename}.wav"
    output_path = os.path.join(voice_files_dir, f"{filename}.wav")

    print(f"正在下载语音文件: {filename}.wav")

    for attempt in range(max_retries):
        try:
            response = requests.get(url, timeout=30, stream=True)
            response.raise_for_status()

            # 确保目录存在
            os.makedirs(voice_files_dir, exist_ok=True)

            # 下载文件
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 验证文件大小
            file_size = os.path.getsize(output_path)
            if file_size > 1024:  # 至少1KB
                print(f"[OK] 下载成功: {filename}.wav ({file_size} bytes)")
                return True
            else:
                print(f"[ERROR] 下载的文件太小: {filename}.wav ({file_size} bytes)")
                os.remove(output_path)

        except requests.exceptions.RequestException as e:
            print(f"[ERROR] 下载失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if os.path.exists(output_path):
                os.remove(output_path)

            if attempt < max_retries - 1:
                print(f"等待 2 秒后重试...")
                time.sleep(2)
        except Exception as e:
            print(f"[ERROR] 下载出错: {e}")
            if os.path.exists(output_path):
                os.remove(output_path)
            break

    print(f"[ERROR] 下载失败: {filename}.wav")
    return False

def download_missing_voice_files(voice_files_dir, voice_list_file):
    """下载缺失的语音文件"""
    print("检查并下载缺失的语音文件...")

    # 读取需要的文件列表
    required_files = []
    if os.path.exists(voice_list_file):
        try:
            with open(voice_list_file, 'r', encoding='utf-8') as f:
                for line in f:
                    filename = line.strip()
                    if filename and not filename.startswith('#'):
                        required_files.append(filename)
        except Exception as e:
            print(f"读取语音列表文件失败: {e}")
            # 使用默认文件列表
            required_files = VOICE_FILES.copy()
    else:
        print("语音列表文件不存在，使用默认文件列表")
        required_files = VOICE_FILES.copy()

    if not required_files:
        print("没有需要下载的语音文件")
        return False

    # 检查并下载缺失的文件
    downloaded_count = 0
    for filename in required_files:
        wav_file = os.path.join(voice_files_dir, f"{filename}.wav")
        if not os.path.exists(wav_file):
            if download_voice_file(filename, voice_files_dir):
                downloaded_count += 1
        else:
            print(f"[OK] 文件已存在: {filename}.wav")

    if downloaded_count > 0:
        print(f"成功下载了 {downloaded_count} 个语音文件")
        return True
    else:
        print("没有下载任何新文件")
        return False

def create_default_voice_list(voice_list_file):
    """创建默认的语音列表文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(voice_list_file), exist_ok=True)

        with open(voice_list_file, 'w', encoding='utf-8') as f:
            f.write("# 预置语音文件列表\n")
            f.write("# 格式：文件名（不含扩展名）\n")
            f.write("# 注释行以#开头\n\n")

            f.write("# 基础提示音\n")
            for filename in VOICE_FILES:
                f.write(f"{filename}\n")

        print(f"[OK] 创建默认语音列表文件: {voice_list_file}")
        return True
    except Exception as e:
        print(f"[ERROR] 创建默认语音列表文件失败: {e}")
        return False

def build_voice_rom(source, target, env):
    """PlatformIO构建钩子函数"""
    print("Building voice ROM...")
    
    # 获取项目根目录
    project_dir = env.get("PROJECT_DIR")
    voice_files_dir = os.path.join(project_dir, "voice_files")
    voice_list_file = os.path.join(voice_files_dir, "voice_list.txt")
    output_file = os.path.join(project_dir, "src", "voice_rom.bin")
    
    # 检查语音文件目录是否存在，如果不存在则创建
    if not os.path.exists(voice_files_dir):
        print(f"语音文件目录不存在，正在创建: {voice_files_dir}")
        os.makedirs(voice_files_dir, exist_ok=True)
    
    # 检查语音文件列表是否存在
    if not os.path.exists(voice_list_file):
        print(f"语音列表文件不存在: {voice_list_file}")
        print("正在创建默认语音列表文件...")
        create_default_voice_list(voice_list_file)

    # 下载缺失的语音文件
    download_missing_voice_files(voice_files_dir, voice_list_file)

    # 从文件列表构建ROM
    create_rom_from_list(voice_files_dir, voice_list_file, output_file)

    # 将ROM文件复制到data目录，以便上传到LittleFS
    copy_rom_to_data_dir(project_dir, output_file)

def create_empty_rom(output_file):
    """创建空的ROM文件"""
    try:
        # 直接创建一个最小的ROM文件结构
        with open(output_file, 'wb') as f:
            # 写入ROM头部（32字节）
            import time
            import hashlib

            magic = 0x564F4943  # "VOIC"
            version_major = 1
            version_minor = 0
            build_number = int(time.time())
            timestamp = int(time.time())
            file_count = 0
            total_size = 32  # 只有头部

            # 计算头部校验和（不包括校验和字段本身）
            header_data = struct.pack('<IHHIII',
                                     magic, version_major, version_minor,
                                     build_number, timestamp, file_count)
            header_data += struct.pack('<I', total_size)
            checksum = zlib.crc32(header_data) & 0xffffffff

            # 写入完整头部
            header = struct.pack('<IHHIIIIII',
                               magic, version_major, version_minor,
                               build_number, timestamp, file_count,
                               total_size, checksum, 0, 0)  # 最后两个0是reserved字段
            f.write(header)

        print(f"Empty voice ROM created: {output_file}")
        return True
    except Exception as e:
        print(f"Failed to create empty voice ROM: {e}")
        return False

def create_rom_from_directory(voice_files_dir, output_file):
    """从目录扫描创建ROM"""
    builder = VoiceROMBuilder()

    # 扫描.wav文件
    voice_dir = Path(voice_files_dir)
    wav_files = list(voice_dir.glob("*.wav"))

    if not wav_files:
        print("目录中没有找到.wav文件，尝试下载默认语音文件...")
        # 下载默认语音文件
        downloaded_count = 0
        for filename in VOICE_FILES:
            if download_voice_file(filename, voice_files_dir):
                downloaded_count += 1

        if downloaded_count == 0:
            print("无法下载任何语音文件，创建空ROM")
            create_empty_rom(output_file)
            return

        # 重新扫描
        wav_files = list(voice_dir.glob("*.wav"))

    # 添加文件到ROM
    for wav_file in wav_files:
        builder.add_file(str(wav_file), VOICE_COMPRESSION_ADPCM)

    # 构建ROM
    if builder.build_rom(output_file):
        print(f"Voice ROM created from directory: {output_file}")
    else:
        print("Failed to create voice ROM from directory")

def create_rom_from_list(voice_files_dir, voice_list_file, output_file):
    """从文件列表创建ROM"""
    builder = VoiceROMBuilder()

    try:
        with open(voice_list_file, 'r', encoding='utf-8') as f:
            for line in f:
                filename = line.strip()
                if filename and not filename.startswith('#'):
                    wav_file = os.path.join(voice_files_dir, filename + ".wav")
                    if os.path.exists(wav_file):
                        builder.add_file(wav_file, VOICE_COMPRESSION_ADPCM)
                    else:
                        print(f"Warning: Voice file not found: {wav_file}")
    except Exception as e:
        print(f"Error reading voice list file: {e}")
        create_empty_rom(output_file)
        return

    # 检查是否有文件被添加
    if not builder.entries:
        print("No voice files found, creating empty ROM")
        create_empty_rom(output_file)
        return

    # 构建ROM
    if builder.build_rom(output_file):
        print(f"Voice ROM created from list: {output_file}")
    else:
        print("Failed to create voice ROM from list")

def copy_rom_to_data_dir(project_dir, rom_file):
    """将ROM文件复制到data目录，以便上传到LittleFS"""
    try:
        data_dir = os.path.join(project_dir, "data")
        os.makedirs(data_dir, exist_ok=True)

        dest_file = os.path.join(data_dir, "voice_rom.bin")

        # 复制文件
        import shutil
        shutil.copy2(rom_file, dest_file)

        print(f"[OK] ROM file copied to data directory: {dest_file}")
        print("  This file will be uploaded to LittleFS and loaded to ROM partition at runtime")

    except Exception as e:
        print(f"[ERROR] Failed to copy ROM file to data directory: {e}")

def main():
    """独立运行时的主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Build voice ROM for BLE Gateway')
    parser.add_argument('--project-dir', required=True, help='Project directory')
    parser.add_argument('--output', help='Output ROM file')
    
    args = parser.parse_args()
    
    voice_files_dir = os.path.join(args.project_dir, "voice_files")
    voice_list_file = os.path.join(voice_files_dir, "voice_list.txt")
    output_file = args.output or os.path.join(args.project_dir, "src", "voice_rom.bin")
    
    # 模拟环境变量
    class MockEnv:
        def get(self, key):
            if key == "PROJECT_DIR":
                return args.project_dir
            return None
    
    build_voice_rom(None, None, MockEnv())

if __name__ == '__main__':
    main()
