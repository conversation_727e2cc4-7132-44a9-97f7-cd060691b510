#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <esp_crc.h>
#include <string.h>
#include <LittleFS.h>

// 前向声明
void voice_rom_deinit(void);

static const char *TAG = "VoiceROM";

// 全局变量
static const esp_partition_t *voice_rom_partition = nullptr;
static voice_rom_header_t rom_header;
static voice_file_entry_t *file_entries = nullptr;
static bool rom_initialized = false;

// 内部函数声明
static uint32_t calculate_checksum(const uint8_t *data, uint32_t size);
static voice_error_t validate_rom_header(const voice_rom_header_t *header);
static voice_error_t load_file_entries(void);
static voice_error_t load_rom_from_filesystem(void);

/**
 * @brief 初始化ROM文件系统
 */
voice_error_t voice_rom_init(void)
{
    if (rom_initialized)
    {
        ESP_LOGW(TAG, "ROM already initialized");
        return VOICE_ERR_OK;
    }

    ESP_LOGI(TAG, "Initializing voice ROM system...");

    // 查找语音ROM分区
    voice_rom_partition = esp_partition_find_first(ESP_PARTITION_TYPE_DATA,
                                                   ESP_PARTITION_SUBTYPE_DATA_SPIFFS,
                                                   VOICE_ROM_PARTITION_NAME);
    if (!voice_rom_partition)
    {
        ESP_LOGE(TAG, "Voice ROM partition not found");
        return VOICE_ERR_PARTITION_NOT_FOUND;
    }

    ESP_LOGI(TAG, "Found voice ROM partition: size=%d bytes", voice_rom_partition->size);

    // 读取ROM头部信息
    esp_err_t err = esp_partition_read(voice_rom_partition, 0, &rom_header, sizeof(rom_header));
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read ROM header: %s", esp_err_to_name(err));
        return VOICE_ERR_INIT_FAILED;
    }

    // 验证ROM头部
    voice_error_t voice_err = validate_rom_header(&rom_header);
    if (voice_err != VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "Invalid ROM header, attempting to load from file system");

        // 尝试从文件系统加载ROM数据
        voice_err = load_rom_from_filesystem();
        if (voice_err != VOICE_ERR_OK)
        {
            ESP_LOGE(TAG, "Failed to load ROM from file system: %d", voice_err);
            return voice_err;
        }

        // 重新读取头部
        err = esp_partition_read(voice_rom_partition, 0, &rom_header, sizeof(rom_header));
        if (err != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to read ROM header after loading: %s", esp_err_to_name(err));
            return VOICE_ERR_INIT_FAILED;
        }

        // 重新验证头部
        voice_err = validate_rom_header(&rom_header);
        if (voice_err != VOICE_ERR_OK)
        {
            ESP_LOGE(TAG, "ROM header still invalid after loading from file system");
            return voice_err;
        }
    }

    ESP_LOGI(TAG, "ROM header validated: version=%d.%d, files=%d, size=%d",
             rom_header.major_version, rom_header.minor_version,
             rom_header.file_count, rom_header.total_size);

    // 加载文件条目
    voice_err = load_file_entries();
    if (voice_err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to load file entries");
        return voice_err;
    }

    rom_initialized = true;
    ESP_LOGI(TAG, "Voice ROM system initialized successfully");
    return VOICE_ERR_OK;
}

/**
 * @brief 验证ROM头部
 */
static voice_error_t validate_rom_header(const voice_rom_header_t *header)
{
    if (!header)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    // 检查魔数
    if (header->magic != VOICE_ROM_MAGIC)
    {
        ESP_LOGE(TAG, "Invalid magic number: 0x%08X", header->magic);
        return VOICE_ERR_INVALID_HEADER;
    }

    // 检查版本
    if (header->major_version > VOICE_ROM_VERSION_MAJOR ||
        (header->major_version == VOICE_ROM_VERSION_MAJOR &&
         header->minor_version > VOICE_ROM_VERSION_MINOR))
    {
        ESP_LOGW(TAG, "ROM version %d.%d is newer than supported %d.%d",
                 header->major_version, header->minor_version,
                 VOICE_ROM_VERSION_MAJOR, VOICE_ROM_VERSION_MINOR);
    }

    // 检查文件数量
    if (header->file_count > MAX_VOICE_FILES)
    {
        ESP_LOGE(TAG, "Too many files in ROM: %d", header->file_count);
        return VOICE_ERR_INVALID_HEADER;
    }

    // 检查总大小
    if (header->total_size > voice_rom_partition->size)
    {
        ESP_LOGE(TAG, "ROM size exceeds partition size: %d > %d",
                 header->total_size, voice_rom_partition->size);
        return VOICE_ERR_INVALID_HEADER;
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 加载文件条目
 */
static voice_error_t load_file_entries(void)
{
    if (rom_header.file_count == 0)
    {
        ESP_LOGW(TAG, "No files in ROM");
        return VOICE_ERR_OK;
    }

    // 分配内存存储文件条目
    size_t entries_size = sizeof(voice_file_entry_t) * rom_header.file_count;
    file_entries = (voice_file_entry_t *)malloc(entries_size);
    if (!file_entries)
    {
        ESP_LOGE(TAG, "Failed to allocate memory for file entries");
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    // 从ROM读取文件条目
    uint32_t entries_offset = sizeof(voice_rom_header_t);
    esp_err_t err = esp_partition_read(voice_rom_partition, entries_offset,
                                       file_entries, entries_size);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read file entries: %s", esp_err_to_name(err));
        free(file_entries);
        file_entries = nullptr;
        return VOICE_ERR_INIT_FAILED;
    }

    // 验证文件条目
    for (uint32_t i = 0; i < rom_header.file_count; i++)
    {
        voice_file_entry_t *entry = &file_entries[i];

        // 确保文件名以null结尾
        entry->filename[MAX_FILENAME_LEN - 1] = '\0';

        ESP_LOGD(TAG, "File %d: %s, offset=%d, compressed=%d, original=%d",
                 i, entry->filename, entry->offset,
                 entry->compressed_size, entry->original_size);
    }

    ESP_LOGI(TAG, "Loaded %d file entries", rom_header.file_count);
    return VOICE_ERR_OK;
}

/**
 * @brief 检查ROM中是否存在文件
 */
bool voice_rom_file_exists(const char *filename)
{
    if (!rom_initialized || !filename || !file_entries)
    {
        return false;
    }

    for (uint32_t i = 0; i < rom_header.file_count; i++)
    {
        if (strcmp(file_entries[i].filename, filename) == 0)
        {
            return true;
        }
    }

    return false;
}

/**
 * @brief 获取ROM文件信息
 */
voice_error_t voice_rom_get_file_entry(const char *filename, voice_file_entry_t *entry)
{
    if (!rom_initialized || !filename || !entry || !file_entries)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    for (uint32_t i = 0; i < rom_header.file_count; i++)
    {
        if (strcmp(file_entries[i].filename, filename) == 0)
        {
            memcpy(entry, &file_entries[i], sizeof(voice_file_entry_t));
            return VOICE_ERR_OK;
        }
    }

    return VOICE_ERR_FILE_NOT_FOUND;
}

/**
 * @brief 从ROM读取文件
 */
voice_error_t voice_rom_read_file(const char *filename, uint8_t *buffer,
                                  uint32_t buffer_size, uint32_t *actual_size)
{
    if (!rom_initialized || !filename || !buffer || !actual_size)
    {
        return VOICE_ERR_INVALID_PARAM;
    }

    // 查找文件条目
    voice_file_entry_t entry;
    voice_error_t err = voice_rom_get_file_entry(filename, &entry);
    if (err != VOICE_ERR_OK)
    {
        return err;
    }

    // 检查缓冲区大小
    uint32_t required_size = (entry.compression_type == VOICE_COMPRESSION_NONE) ? entry.original_size : entry.compressed_size;

    if (buffer_size < required_size)
    {
        ESP_LOGE(TAG, "Buffer too small: need %d, got %d", required_size, buffer_size);
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    // 从ROM读取文件数据
    esp_err_t esp_err = esp_partition_read(voice_rom_partition, entry.offset,
                                           buffer, required_size);
    if (esp_err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read file data: %s", esp_err_to_name(esp_err));
        return VOICE_ERR_INIT_FAILED;
    }

    // 验证校验和
    uint32_t calculated_checksum = calculate_checksum(buffer, required_size);
    if (calculated_checksum != entry.checksum)
    {
        ESP_LOGE(TAG, "Checksum mismatch for file %s: expected 0x%08X, got 0x%08X",
                 filename, entry.checksum, calculated_checksum);
        return VOICE_ERR_CHECKSUM_MISMATCH;
    }

    *actual_size = required_size;
    ESP_LOGD(TAG, "Successfully read file %s: %d bytes", filename, required_size);
    return VOICE_ERR_OK;
}

/**
 * @brief 计算校验和
 */
static uint32_t calculate_checksum(const uint8_t *data, uint32_t size)
{
    return esp_crc32_le(0, data, size);
}

/**
 * @brief 获取ROM系统状态
 */
voice_error_t voice_rom_get_info(uint32_t *total_size, uint32_t *used_size, uint16_t *file_count)
{
    if (!rom_initialized)
    {
        return VOICE_ERR_INIT_FAILED;
    }

    if (total_size)
    {
        *total_size = voice_rom_partition->size;
    }

    if (used_size)
    {
        *used_size = rom_header.total_size;
    }

    if (file_count)
    {
        *file_count = rom_header.file_count;
    }

    return VOICE_ERR_OK;
}

/**
 * @brief 从文件系统加载ROM数据到分区
 */
static voice_error_t load_rom_from_filesystem(void)
{
    ESP_LOGI(TAG, "Loading ROM data from file system...");

    // 尝试从LittleFS加载ROM文件
    const char *rom_file_path = "/voice_rom.bin";

    // 首先检查LittleFS中是否有ROM文件
    if (!LittleFS.exists(rom_file_path))
    {
        ESP_LOGW(TAG, "ROM file not found in LittleFS: %s", rom_file_path);
        return VOICE_ERR_FILE_NOT_FOUND;
    }

    File rom_file = LittleFS.open(rom_file_path, "r");
    if (!rom_file)
    {
        ESP_LOGE(TAG, "Failed to open ROM file: %s", rom_file_path);
        return VOICE_ERR_FILE_NOT_FOUND;
    }

    size_t file_size = rom_file.size();
    ESP_LOGI(TAG, "ROM file size: %d bytes", file_size);

    // 检查文件大小是否超过分区大小
    if (file_size > voice_rom_partition->size)
    {
        ESP_LOGE(TAG, "ROM file too large: %d > %d", file_size, voice_rom_partition->size);
        rom_file.close();
        return VOICE_ERR_STORAGE_FULL;
    }

    // 分配临时缓冲区
    uint8_t *buffer = (uint8_t *)malloc(file_size);
    if (!buffer)
    {
        ESP_LOGE(TAG, "Failed to allocate buffer for ROM data: %d bytes", file_size);
        rom_file.close();
        return VOICE_ERR_INSUFFICIENT_MEMORY;
    }

    // 读取文件数据
    size_t bytes_read = rom_file.readBytes((char *)buffer, file_size);
    rom_file.close();

    if (bytes_read != file_size)
    {
        ESP_LOGE(TAG, "Failed to read complete ROM file: %d/%d bytes", bytes_read, file_size);
        free(buffer);
        return VOICE_ERR_INIT_FAILED;
    }

    // 擦除分区
    esp_err_t err = esp_partition_erase_range(voice_rom_partition, 0, voice_rom_partition->size);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to erase ROM partition: %s", esp_err_to_name(err));
        free(buffer);
        return VOICE_ERR_INIT_FAILED;
    }

    // 写入数据到分区
    err = esp_partition_write(voice_rom_partition, 0, buffer, file_size);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to write ROM data to partition: %s", esp_err_to_name(err));
        free(buffer);
        return VOICE_ERR_INIT_FAILED;
    }

    free(buffer);
    ESP_LOGI(TAG, "Successfully loaded ROM data from file system to partition");
    return VOICE_ERR_OK;
}

/**
 * @brief 反初始化ROM系统
 */
void voice_rom_deinit(void)
{
    if (file_entries)
    {
        free(file_entries);
        file_entries = nullptr;
    }

    rom_initialized = false;
    ESP_LOGI(TAG, "Voice ROM system deinitialized");
}
