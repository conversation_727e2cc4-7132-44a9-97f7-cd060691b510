#!/usr/bin/env python3
"""
语音ROM系统修复验证脚本
用于验证语音播报混合系统的ROM访问修复是否有效
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f" {title}")
    print("="*60)

def print_step(step, description):
    """打印步骤"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✓ {description}: {filepath} ({size} bytes)")
        return True
    else:
        print(f"✗ {description}: {filepath} (不存在)")
        return False

def run_command(cmd, description, timeout=60):
    """运行命令并返回结果"""
    print(f"执行: {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, 
                              text=True, timeout=timeout, cwd=".")
        if result.returncode == 0:
            print(f"✓ {description} - 成功")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ {description} - 失败 (返回码: {result.returncode})")
            if result.stderr.strip():
                print(f"错误: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"✗ {description} - 超时")
        return False
    except Exception as e:
        print(f"✗ {description} - 异常: {e}")
        return False

def main():
    """主函数"""
    print_header("BLE网关语音ROM系统修复验证")
    
    # 检查项目结构
    print_step(1, "检查项目文件结构")
    
    files_to_check = [
        ("voice_files/voice_list.txt", "语音文件列表"),
        ("src/voice_hybrid_system.cpp", "语音混合系统核心"),
        ("src/voice_rom_system.cpp", "ROM文件系统"),
        ("src/voice_rom_test.cpp", "ROM测试代码"),
        ("src/scripts/build_voice_rom.py", "ROM构建脚本"),
        ("include/voice_hybrid_system.h", "系统头文件"),
        ("ble_gateway_partition_v2.csv", "分区表配置"),
        ("platformio.ini", "PlatformIO配置")
    ]
    
    missing_files = 0
    for filepath, description in files_to_check:
        if not check_file_exists(filepath, description):
            missing_files += 1
    
    if missing_files > 0:
        print(f"\n❌ 发现 {missing_files} 个缺失文件，请检查项目完整性")
        return False
    
    # 检查语音文件
    print_step(2, "检查语音文件")
    
    voice_files_dir = "voice_files"
    if os.path.exists(voice_files_dir):
        wav_files = [f for f in os.listdir(voice_files_dir) if f.endswith('.wav')]
        print(f"✓ 找到 {len(wav_files)} 个WAV文件:")
        for wav_file in wav_files:
            filepath = os.path.join(voice_files_dir, wav_file)
            size = os.path.getsize(filepath)
            print(f"  - {wav_file} ({size} bytes)")
    else:
        print("✗ 语音文件目录不存在")
        return False
    
    # 构建ROM文件
    print_step(3, "构建语音ROM文件")
    
    if not run_command("python src/scripts/build_voice_rom.py --project-dir .", 
                      "构建语音ROM"):
        print("❌ ROM构建失败")
        return False
    
    # 检查生成的文件
    print_step(4, "验证生成的文件")
    
    generated_files = [
        ("src/voice_rom.bin", "源码目录ROM文件"),
        ("data/voice_rom.bin", "数据目录ROM文件")
    ]
    
    for filepath, description in generated_files:
        if not check_file_exists(filepath, description):
            print(f"❌ {description} 生成失败")
            return False
    
    # 验证ROM文件内容
    print_step(5, "验证ROM文件内容")
    
    rom_file = "src/voice_rom.bin"
    if os.path.exists(rom_file):
        with open(rom_file, 'rb') as f:
            # 读取头部
            magic = f.read(4)
            magic_int = int.from_bytes(magic, 'little')
            expected_magic = 0x564F4943  # "VOIC"
            if magic_int == expected_magic:
                print("✓ ROM文件魔数正确")
                
                # 读取版本和文件数量
                f.seek(4)
                version_data = f.read(8)  # major(2) + minor(2) + build(4)
                major = int.from_bytes(version_data[0:2], 'little')
                minor = int.from_bytes(version_data[2:4], 'little')
                build = int.from_bytes(version_data[4:8], 'little')
                print(f"✓ ROM版本: {major}.{minor} (构建号: {build})")
                
                f.seek(16)
                file_count_data = f.read(4)
                file_count = int.from_bytes(file_count_data, 'little')
                print(f"✓ ROM包含 {file_count} 个文件")
                
                if file_count > 0:
                    print("✓ ROM文件结构验证通过")
                else:
                    print("⚠ ROM文件中没有语音文件")
            else:
                print(f"✗ ROM文件魔数错误: {magic} (期望: {expected_magic:08X})")
                return False
    
    # 检查分区配置
    print_step(6, "验证分区配置")
    
    partition_file = "ble_gateway_partition_v2.csv"
    if os.path.exists(partition_file):
        with open(partition_file, 'r') as f:
            content = f.read()
            if 'voice_rom' in content and '0x50000' in content:
                print("✓ 分区表包含voice_rom分区配置")
                print("✓ voice_rom分区大小: 320KB (0x50000)")
            else:
                print("✗ 分区表配置不正确")
                return False
    
    # 检查PlatformIO配置
    print_step(7, "验证PlatformIO配置")
    
    platformio_file = "platformio.ini"
    if os.path.exists(platformio_file):
        with open(platformio_file, 'r') as f:
            content = f.read()
            checks = [
                ('ble_gateway_partition_v2.csv', '分区表配置'),
                ('build_voice_rom.py', 'ROM构建脚本'),
                ('VOICE_HYBRID_SYSTEM_ENABLED=1', '语音混合系统启用'),
                ('board_build.filesystem = littlefs', 'LittleFS文件系统')
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"✓ {description}")
                else:
                    print(f"⚠ {description} - 可能缺失")
    
    # 总结
    print_step(8, "修复效果总结")
    
    print("🔧 已实施的修复措施:")
    print("  1. 修复了Python构建脚本中的压缩类型不匹配问题")
    print("  2. 添加了从LittleFS自动加载ROM数据到分区的功能")
    print("  3. 改进了ROM系统初始化逻辑，支持运行时加载")
    print("  4. 配置了LittleFS文件系统支持")
    print("  5. 创建了完整的测试和验证代码")
    
    print("\n📋 使用说明:")
    print("  1. 编译项目: pio run -e esp32-s3-devkitc-1-N16R8")
    print("  2. 上传文件系统: pio run -e esp32-s3-devkitc-1-N16R8 -t uploadfs")
    print("  3. 上传固件: pio run -e esp32-s3-devkitc-1-N16R8 -t upload")
    print("  4. 监控串口输出查看测试结果")
    
    print("\n🎯 预期效果:")
    print("  - 系统启动时会自动检测ROM分区")
    print("  - 如果分区为空，会从LittleFS加载ROM数据")
    print("  - 语音播放时优先使用ROM中的文件")
    print("  - 串口会输出详细的测试和状态信息")
    
    print_header("验证完成")
    print("✅ 语音ROM系统修复验证通过！")
    print("📝 请按照上述使用说明进行测试")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
