; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-s3-devkitc-1-N16R8]
platform = espressif32 @ 6.6.0
framework = arduino
board = esp32-s3-devkitc-1-N16R8
board_build.arduino.partitions = ble_gateway_partition_v2.csv
build_flags =
	-Wall -Wextra
	-D CORE_DEBUG_LEVEL=ARDUHAL_LOG_LEVEL_DEBUG
	-D CONFIG_NIMBLE_CPP_LOG_LEVEL=0
	-D LV_LVGL_H_INCLUDE_SIMPLE
	-I "src/lvgl/custom"
	-I "src/lvgl/generated"
	-Os
	-DBOARD_HAS_PSRAM
	-mfix-esp32-psram-cache-issue
	-DCONFIG_SPIRAM_CACHE_WORKAROUND
	-D CONFIG_ARDUHAL_LOG_COLORS
	-D CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN=8192
	-D CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN=8192
	; -D CONFIG_MBEDTLS_CERTIFICATE_BUNDLE=y
	; -D CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL=y
	-D CONFIG_ESP32_WIFI_BT_COEXIST_ENABLE=y
	-D VOICE_HYBRID_SYSTEM_ENABLED=1
	-D VOICE_ROM_PARTITION_SIZE=0x50000
	-D VOICE_CACHE_SIZE_KB=64
	-D VOICE_COMPRESSION_ENABLED=1

extra_scripts =
	src/scripts/generate_cert_bundle.py
	pre:src/scripts/build_voice_rom.py
	src/scripts/upload_voice_rom.py
board_ssl_cert_source = mozilla
board_build.embed_files = src/certs/x509_crt_bundle.bin

# LittleFS文件系统配置
board_build.filesystem = littlefs

lib_deps =
	knolleary/PubSubClient@^2.8
	bblanchon/ArduinoJson@^6.19.4
	arduino-libraries/NTPClient@^3.2.1
	h2zero/NimBLE-Arduino@^1.4.1
	lvgl/lvgl@8.3.11
	bodmer/TFT_eSPI@^2.4.79
	mathertel/OneButton@^2.0.3
	esphome/ESP32-audioI2S@^2.0.7
	elims/PsychicMqttClient@^0.2.0
