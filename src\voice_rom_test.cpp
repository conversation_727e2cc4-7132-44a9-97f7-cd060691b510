/**
 * @file voice_rom_test.cpp
 * @brief 语音ROM系统测试函数
 * 
 * 用于验证ROM语音文件访问功能是否正常工作
 */

#include "voice_hybrid_system.h"
#include <esp_log.h>
#include <LittleFS.h>

static const char *TAG = "VoiceROMTest";

/**
 * @brief 测试ROM系统初始化
 */
bool test_rom_initialization()
{
    ESP_LOGI(TAG, "Testing ROM system initialization...");
    
    voice_error_t err = voice_rom_init();
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "ROM initialization failed: %d", err);
        return false;
    }
    
    ESP_LOGI(TAG, "✓ ROM system initialized successfully");
    return true;
}

/**
 * @brief 测试ROM系统信息获取
 */
bool test_rom_info()
{
    ESP_LOGI(TAG, "Testing ROM system info...");
    
    uint32_t total_size, used_size;
    uint16_t file_count;
    
    voice_error_t err = voice_rom_get_info(&total_size, &used_size, &file_count);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to get ROM info: %d", err);
        return false;
    }
    
    ESP_LOGI(TAG, "✓ ROM Info - Total: %d bytes, Used: %d bytes, Files: %d", 
             total_size, used_size, file_count);
    
    if (file_count == 0)
    {
        ESP_LOGW(TAG, "Warning: No files found in ROM");
        return false;
    }
    
    return true;
}

/**
 * @brief 测试ROM文件存在性检查
 */
bool test_rom_file_exists()
{
    ESP_LOGI(TAG, "Testing ROM file existence...");
    
    const char* test_files[] = {
        "open_app_to_config",
        "network_success", 
        "select_user",
        "blood_pressure_data",
        "temperature_data"
    };
    
    int found_count = 0;
    for (int i = 0; i < 5; i++)
    {
        bool exists = voice_rom_file_exists(test_files[i]);
        ESP_LOGI(TAG, "File '%s': %s", test_files[i], exists ? "EXISTS" : "NOT FOUND");
        if (exists) found_count++;
    }
    
    ESP_LOGI(TAG, "✓ Found %d/5 test files in ROM", found_count);
    return found_count > 0;
}

/**
 * @brief 测试ROM文件读取
 */
bool test_rom_file_read()
{
    ESP_LOGI(TAG, "Testing ROM file reading...");
    
    const char* test_file = "network_success";
    
    // 检查文件是否存在
    if (!voice_rom_file_exists(test_file))
    {
        ESP_LOGE(TAG, "Test file '%s' not found in ROM", test_file);
        return false;
    }
    
    // 获取文件信息
    voice_file_entry_t entry;
    voice_error_t err = voice_rom_get_file_entry(test_file, &entry);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to get file entry for '%s': %d", test_file, err);
        return false;
    }
    
    ESP_LOGI(TAG, "File info - Size: %d bytes, Compressed: %d bytes, Type: %d", 
             entry.original_size, entry.compressed_size, entry.compression_type);
    
    // 分配缓冲区读取文件
    uint32_t buffer_size = entry.compressed_size;
    uint8_t* buffer = (uint8_t*)malloc(buffer_size);
    if (!buffer)
    {
        ESP_LOGE(TAG, "Failed to allocate buffer for file reading");
        return false;
    }
    
    uint32_t actual_size;
    err = voice_rom_read_file(test_file, buffer, buffer_size, &actual_size);
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Failed to read file '%s': %d", test_file, err);
        free(buffer);
        return false;
    }
    
    ESP_LOGI(TAG, "✓ Successfully read file '%s': %d bytes", test_file, actual_size);
    free(buffer);
    return true;
}

/**
 * @brief 测试语音混合系统
 */
bool test_voice_hybrid_system()
{
    ESP_LOGI(TAG, "Testing voice hybrid system...");
    
    voice_error_t err = voice_hybrid_init();
    if (err != VOICE_ERR_OK)
    {
        ESP_LOGE(TAG, "Voice hybrid system initialization failed: %d", err);
        return false;
    }
    
    // 获取存储信息
    voice_storage_info_t storage_info;
    err = voice_get_storage_info(&storage_info);
    if (err == VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "Storage Info:");
        ESP_LOGI(TAG, "  ROM: %d/%d bytes, %d files", 
                 storage_info.rom_used_size, storage_info.rom_total_size, storage_info.rom_file_count);
        ESP_LOGI(TAG, "  LittleFS: %d/%d bytes, %d files", 
                 storage_info.littlefs_used_size, storage_info.littlefs_total_size, storage_info.littlefs_file_count);
        ESP_LOGI(TAG, "  Cache: %d/%d bytes", 
                 storage_info.cache_used_size, storage_info.cache_total_size);
    }
    
    // 测试文件信息获取
    const char* test_file = "network_success";
    voice_file_info_t file_info;
    err = voice_get_file_info(test_file, &file_info);
    if (err == VOICE_ERR_OK)
    {
        ESP_LOGI(TAG, "File '%s' info - Source: %d, Size: %d bytes, Version: %d", 
                 test_file, file_info.source, file_info.file_size, file_info.version);
    }
    
    ESP_LOGI(TAG, "✓ Voice hybrid system test completed");
    return true;
}

/**
 * @brief 运行所有ROM测试
 */
void run_voice_rom_tests()
{
    ESP_LOGI(TAG, "=== Starting Voice ROM System Tests ===");
    
    int passed = 0;
    int total = 5;
    
    if (test_rom_initialization()) passed++;
    if (test_rom_info()) passed++;
    if (test_rom_file_exists()) passed++;
    if (test_rom_file_read()) passed++;
    if (test_voice_hybrid_system()) passed++;
    
    ESP_LOGI(TAG, "=== Test Results: %d/%d tests passed ===", passed, total);
    
    if (passed == total)
    {
        ESP_LOGI(TAG, "🎉 All tests PASSED! ROM system is working correctly.");
    }
    else
    {
        ESP_LOGE(TAG, "❌ Some tests FAILED. Please check the logs above.");
    }
}

/**
 * @brief 语音系统状态报告
 */
void voice_system_status_report()
{
    ESP_LOGI(TAG, "=== Voice System Status Report ===");
    
    // 检查LittleFS中的ROM文件
    if (LittleFS.exists("/voice_rom.bin"))
    {
        File rom_file = LittleFS.open("/voice_rom.bin", "r");
        if (rom_file)
        {
            ESP_LOGI(TAG, "✓ ROM file found in LittleFS: %d bytes", rom_file.size());
            rom_file.close();
        }
    }
    else
    {
        ESP_LOGW(TAG, "⚠ ROM file not found in LittleFS");
    }
    
    // 运行测试
    run_voice_rom_tests();
}
